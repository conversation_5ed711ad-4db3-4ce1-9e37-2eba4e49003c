import {Ambiente} from "./Ambiente";
import {Resposta} from "../utils/Resposta";
import {I18nConfig} from "../lib/instagram/i18n.config";
import axios from 'axios';

export class InstagramDirectService {
  private static _instance: InstagramDirectService;

  private appId: string;
  private appSecret: string;

  private constructor() {
    const config = Ambiente.Instance.config?.instagramDirect;
    console.log('[InstagramDirect] Configuração carregada:', config);

    // Usar configuração se disponível, caso contrário usar valores padrão
    this.appId = config?.appId;
    this.appSecret = config?.appSecret;

    console.log('[InstagramDirect] App ID configurado:', this.appId);
    console.log('[InstagramDirect] Configuração carregada com sucesso (fallback aplicado se necessário)');
  }

  public static get Instance(): InstagramDirectService {
    return this._instance || (this._instance = new InstagramDirectService());
  }

  /**
   * Gera URL de autorização para Instagram Basic Display API
   */
  obtenhaUrlAutorizacao(baseUrl?: string): string {
    console.log('[InstagramDirect] Gerando URL de autorização');
    console.log('[InstagramDirect] App ID:', this.appId);

    // Usar exatamente os mesmos parâmetros da URL fornecida
    const scopes = 'instagram_business_basic%2Cinstagram_business_manage_messages%2Cinstagram_business_manage_comments%2Cinstagram_business_content_publish%2Cinstagram_business_manage_insights';

    // Construir redirect_uri dinamicamente se baseUrl for fornecida
    let redirectUri = 'https://localhost:8443/admin/instagram-bot/config-instagram-direct';
    if (baseUrl) {
      const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
      redirectUri = `${cleanBaseUrl}/admin/instagram-bot/admin/instagram-bot`;
    }

    // URL exata conforme solicitado, mas com redirect_uri dinâmico
    const url = 'https://www.instagram.com/oauth/authorize?force_reauth=true&client_id=1245407234253530&redirect_uri=' + (redirectUri) + '&response_type=code&' +
      'scope=instagram_business_basic%2Cinstagram_business_manage_messages%2Cinstagram_business_manage_comments%2Cinstagram_business_content_publish%2Cinstagram_business_manage_insights';

    //const url = `${baseUrl}?${params.join('&')}`;
    console.log('[InstagramDirect] URL gerada:', url);

    return url;
  }

  /**
   * Troca código de autorização por access token usando Instagram Graph API
   */
  async troqueCodigoPorToken(code: string): Promise<Resposta<any>> {
    try {
      console.log('[InstagramDirect] Trocando código por token');
      console.log('[InstagramDirect] Timestamp:', new Date().toISOString());
      console.log('[InstagramDirect] Code completo:', code);
      console.log('[InstagramDirect] Code length:', code?.length);
      console.log('[InstagramDirect] App ID:', this.appId);

      // Validar se o código não está vazio
      if (!code || code.trim().length === 0) {
        console.error('[InstagramDirect] Código vazio ou inválido');
        return Resposta.erro('Código de autorização vazio ou inválido');
      }

      // Construir redirect_uri dinamicamente (mesmo padrão usado em obtenhaUrlAutorizacao)
      const redirectUri = 'https://localhost:8443/admin/instagram-bot/config-instagram-direct';

      // Criar FormData para application/x-www-form-urlencoded
      const formData = new URLSearchParams();
      formData.append('client_id', this.appId);
      formData.append('client_secret', this.appSecret);
      formData.append('grant_type', 'authorization_code');
      formData.append('redirect_uri', redirectUri);
      formData.append('code', code.trim());

      console.log('[InstagramDirect] Dados enviados (sem secret):', {
        client_id: this.appId,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
        code: code.trim()
      });

      // Usar Instagram Graph API para trocar código por token
      const response = await axios.post('https://api.instagram.com/oauth/access_token', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      console.log('[InstagramDirect] Token obtido com sucesso:', response.data);

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      console.error('[InstagramDirect] Erro detalhado ao trocar código por token:');
      console.error('[InstagramDirect] Status:', error.response?.status);
      console.error('[InstagramDirect] Headers:', error.response?.headers);
      console.error('[InstagramDirect] Data:', error.response?.data);
      console.error('[InstagramDirect] Message:', error.message);
      console.error('[InstagramDirect] Full error:', error);

      // Extrair mensagem de erro mais específica
      let mensagemErro = 'Erro ao obter token de acesso';

      if (error.response?.data) {
        if (error.response.data.error_message) {
          mensagemErro = error.response.data.error_message;
        } else if (error.response.data.error?.message) {
          mensagemErro = error.response.data.error.message;
        } else if (error.response.data.error_description) {
          mensagemErro = error.response.data.error_description;
        } else if (typeof error.response.data === 'string') {
          mensagemErro = error.response.data;
        }
      }

      console.error('[InstagramDirect] Mensagem de erro final:', mensagemErro);

      return Resposta.erro(mensagemErro);
    }
  }

  /**
   * Converte short-lived token em long-lived token (60 dias)
   */
  async estenderToken(shortLivedToken: string): Promise<Resposta<any>> {
    try {
      console.log('[InstagramDirect] Estendendo token para long-lived');

      const params = {
        grant_type: 'ig_exchange_token',
        client_secret: this.appSecret,
        access_token: shortLivedToken
      };

      const response = await axios.get('https://graph.instagram.com/access_token', { params });

      console.log('[InstagramDirect] Token estendido com sucesso');

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      console.error('[InstagramDirect] Erro ao estender token:', error.response?.data || error.message);
      return Resposta.erro(error.response?.data?.error?.message || 'Erro ao estender token');
    }
  }

  /**
   * Renova long-lived token (deve ser feito antes de expirar)
   */
  async renovarToken(accessToken: string): Promise<Resposta<any>> {
    try {
      console.log('[InstagramDirect] Renovando token');

      const params = {
        grant_type: 'ig_refresh_token',
        access_token: accessToken
      };

      const response = await axios.get('https://graph.instagram.com/refresh_access_token', { params });

      console.log('[InstagramDirect] Token renovado com sucesso');

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      console.error('[InstagramDirect] Erro ao renovar token:', error.response?.data || error.message);
      return Resposta.erro(error.response?.data?.error?.message || 'Erro ao renovar token');
    }
  }

  /**
   * Obtém dados do perfil do usuário via Instagram Basic Display API
   */
  async obtenhaPerfilUsuario(accessToken: string, userId: string): Promise<Resposta<any>> {
    try {
      console.log('[InstagramDirect] Obtendo dados do perfil');
      console.log('[InstagramDirect] User ID:', userId);
      console.log('[InstagramDirect] Access Token (primeiros 20 chars):', accessToken.substring(0, 20) + '...');

      const params = {
        fields: 'id,username,account_type,media_count',
        access_token: accessToken
      };

      // Usar Instagram Basic Display API ao invés da Graph API
      const url = `https://graph.instagram.com/me`;
      console.log('[InstagramDirect] URL da requisição:', url);

      const response = await axios.get(url, { params });

      console.log('[InstagramDirect] Dados do perfil obtidos com sucesso:', response.data);

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      console.error('[InstagramDirect] Erro detalhado ao obter perfil:');
      console.error('[InstagramDirect] Status:', error.response?.status);
      console.error('[InstagramDirect] Headers:', error.response?.headers);
      console.error('[InstagramDirect] Data:', error.response?.data);
      console.error('[InstagramDirect] Message:', error.message);

      let mensagemErro = 'Erro ao obter dados do perfil';
      if (error.response?.data?.error?.message) {
        mensagemErro = error.response.data.error.message;
      }

      return Resposta.erro(mensagemErro);
    }
  }

  /**
   * Obtém mídia do usuário
   */
  async obtenhaMidiaUsuario(accessToken: string, limit: number = 25): Promise<Resposta<any>> {
    try {
      console.log('[InstagramDirect] Obtendo mídia do usuário');

      const params = {
        fields: 'id,caption,media_type,media_url,thumbnail_url,permalink,timestamp',
        limit: limit.toString(),
        access_token: accessToken
      };

      const response = await axios.get('https://graph.instagram.com/me/media', { params });

      console.log('[InstagramDirect] Mídia obtida com sucesso');

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      console.error('[InstagramDirect] Erro ao obter mídia:', error.response?.data || error.message);
      return Resposta.erro(error.response?.data?.error?.message || 'Erro ao obter mídia');
    }
  }

  /**
   * Testa se o token ainda é válido
   */
  async testeToken(accessToken: string): Promise<Resposta<any>> {
    try {
      console.log('[InstagramDirect] Testando validade do token');

      const resultado = await this.obtenhaPerfilUsuario(accessToken, 'me');

      if (resultado.sucesso) {
        console.log('[InstagramDirect] Token válido');
        return Resposta.sucesso({ valido: true, dados: resultado.data });
      } else {
        console.log('[InstagramDirect] Token inválido');
        return Resposta.sucesso({ valido: false, erro: resultado.erro });
      }

    } catch (error: any) {
      console.error('[InstagramDirect] Erro ao testar token:', error);
      return Resposta.erro('Erro ao testar token');
    }
  }

  /**
   * Gera estado aleatório para segurança OAuth
   */
  private gerarEstadoAleatorio(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Calcula data de expiração do token (60 dias a partir de agora)
   */
  calculeDataExpiracao(): Date {
    const agora = new Date();
    const sessenta_dias = 60 * 24 * 60 * 60 * 1000; // 60 dias em millisegundos
    return new Date(agora.getTime() + sessenta_dias);
  }

  /**
   * Habilita webhook subscriptions para receber notificações do Instagram
   * Necessário após conectar a conta (Step 3 da documentação do Instagram)
   */
  async habilitarSubscriptions(userId: string, accessToken: string): Promise<Resposta<any>> {
    const startTime = new Date().getTime();

    try {
      console.log('[InstagramDirect] =====================================');
      console.log('[InstagramDirect] INICIANDO PROCESSO DE WEBHOOK SUBSCRIPTIONS');
      console.log('[InstagramDirect] Timestamp:', new Date().toISOString());
      console.log('[InstagramDirect] Page ID:', userId);
      console.log('[InstagramDirect] Access Token (primeiros 20 chars):', accessToken.substring(0, 20) + '...');
      console.log('[InstagramDirect] Access Token length:', accessToken.length);

      // Subscriptions necessárias para Instagram Direct Messages
      const subscriptions = [
        'messages'
      ];

      console.log('[InstagramDirect] STEP 1: Preparando parâmetros de subscription');
      console.log('[InstagramDirect] Subscriptions disponíveis:', subscriptions);

      // Criar string de subscribed_fields
      const subscribedFields = subscriptions.join(',');
      console.log('[InstagramDirect] Subscribed fields concatenados:', subscribedFields);

      const params = {
        subscribed_fields: subscribedFields,
        access_token: accessToken
      };

      console.log('[InstagramDirect] STEP 2: Parâmetros da requisição preparados');
      console.log('[InstagramDirect] Parâmetros (sem access_token):', {
        subscribed_fields: subscribedFields
      });


      // Endpoint para habilitar subscriptions (Instagram Graph API)
      const apiVersion = 'v23.0';
      const url = `https://graph.instagram.com/${apiVersion}/${userId}/subscribed_apps`;

      console.log('[InstagramDirect] STEP 3: Configurando requisição HTTP');
      console.log('[InstagramDirect] API Version:', apiVersion);
      console.log('[InstagramDirect] URL completa:', url);
      console.log('[InstagramDirect] Método HTTP: POST');
      console.log('[InstagramDirect] Content-Type: application/x-www-form-urlencoded (via params)');

      console.log('[InstagramDirect] STEP 4: Enviando requisição para Instagram API...');
      const requestStartTime = new Date().getTime();

      const response = await axios.post(url, null, { params });

      const requestEndTime = new Date().getTime();
      const requestDuration = requestEndTime - requestStartTime;

      console.log('[InstagramDirect] STEP 5: Resposta recebida com sucesso!');
      console.log('[InstagramDirect] Tempo da requisição:', requestDuration + 'ms');
      console.log('[InstagramDirect] Status da resposta:', response.status);
      console.log('[InstagramDirect] Headers da resposta:', response.headers);
      console.log('[InstagramDirect] Dados da resposta completos:', JSON.stringify(response.data, null, 2));

      const totalTime = new Date().getTime() - startTime;
      console.log('[InstagramDirect] SUCESSO: Subscriptions habilitadas em', totalTime + 'ms');
      console.log('[InstagramDirect] =====================================');

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      const totalTime = new Date().getTime() - startTime;

      console.error('[InstagramDirect] =====================================');
      console.error('[InstagramDirect] ERRO AO HABILITAR SUBSCRIPTIONS');
      console.error('[InstagramDirect] Tempo até erro:', totalTime + 'ms');
      console.error('[InstagramDirect] Timestamp do erro:', new Date().toISOString());

      // Log detalhado do erro
      console.error('[InstagramDirect] Erro completo:', error);
      console.error('[InstagramDirect] Error name:', error.name);
      console.error('[InstagramDirect] Error message:', error.message);
      console.error('[InstagramDirect] Error stack:', error.stack);

      if (error.response) {
        console.error('[InstagramDirect] Resposta de erro HTTP:');
        console.error('[InstagramDirect] Status:', error.response.status);
        console.error('[InstagramDirect] Status text:', error.response.statusText);
        console.error('[InstagramDirect] Headers:', error.response.headers);
        console.error('[InstagramDirect] Data completa:', JSON.stringify(error.response.data, null, 2));

        // Log do request que falhou
        if (error.config) {
          console.error('[InstagramDirect] Request que falhou:');
          console.error('[InstagramDirect] URL:', error.config.url);
          console.error('[InstagramDirect] Method:', error.config.method);
          console.error('[InstagramDirect] Params:', error.config.params);
          console.error('[InstagramDirect] Headers enviados:', error.config.headers);
        }

        // Log de trace IDs se disponível
        if (error.response.data?.error?.fbtrace_id) {
          console.error('[InstagramDirect] Facebook Trace ID:', error.response.data.error.fbtrace_id);
        }
      } else if (error.request) {
        console.error('[InstagramDirect] Erro de rede/timeout:');
        console.error('[InstagramDirect] Request:', error.request);
      }

      let mensagemErro = 'Erro ao habilitar webhook subscriptions';
      if (error.response?.data?.error?.message) {
        mensagemErro = error.response.data.error.message;
      }

      console.error('[InstagramDirect] Mensagem de erro final:', mensagemErro);
      console.error('[InstagramDirect] =====================================');

      return Resposta.erro(mensagemErro);
    }
  }

  /**
   * Verifica se as subscriptions estão ativas para a página
   */
  async verificarSubscriptions(pageId: string, accessToken: string): Promise<Resposta<any>> {
    try {
      console.log('[InstagramDirect] Verificando webhook subscriptions');

      const params = {
        access_token: accessToken
      };

      const url = `https://graph.instagram.com/v21.0/${pageId}/subscribed_apps`;

      const response = await axios.get(url, { params });

      console.log('[InstagramDirect] Status das subscriptions:', response.data);

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      console.error('[InstagramDirect] Erro ao verificar subscriptions:', error.response?.data || error.message);
      return Resposta.erro(error.response?.data?.error?.message || 'Erro ao verificar subscriptions');
    }
  }

  /**
   * Define ice-breakers para Instagram Direct usando Instagram Graph API
   */
  async definaIceBreakers(accessToken: string, userId: string): Promise<Resposta<any>> {
    const startTime = new Date().getTime();

    try {
      console.log('[InstagramDirect] =====================================');
      console.log('[InstagramDirect] INICIANDO CONFIGURAÇÃO DE ICE-BREAKERS');
      console.log('[InstagramDirect] Timestamp:', new Date().toISOString());
      console.log('[InstagramDirect] User ID:', userId);
      console.log('[InstagramDirect] Access Token (primeiros 20 chars):', accessToken.substring(0, 20) + '...');

      const i18n = I18nConfig.Instance.i18n();

      // Configurar ice-breakers seguindo o formato correto da API do Instagram Direct
      // Usar formato simples (question, payload) ao invés de call_to_actions
      const iceBreakersConfig = {
        platform: 'instagram',
        ice_breakers: [
          {
            question: i18n.__("menu.fazer_pedido"),
            payload: "FAZER_PEDIDO"
          },
          {
            question: i18n.__("menu.cardapio"),
            payload: "CARDAPIO"
          },
          {
            question: i18n.__("menu.horario_atendimento"),
            payload: "HORARIO_ATENDIMENTO"
          }
        ]
      };

      console.log('[InstagramDirect] STEP 1: Configuração dos ice-breakers preparada');
      console.log('[InstagramDirect] Ice-breakers config:', JSON.stringify(iceBreakersConfig, null, 2));

      // Endpoint para configurar ice-breakers no Instagram Direct
      const apiVersion = 'v23.0';
      const url = `https://graph.instagram.com/${apiVersion}/${userId}/messenger_profile`;

      console.log('[InstagramDirect] STEP 2: Configurando requisição HTTP');
      console.log('[InstagramDirect] API Version:', apiVersion);
      console.log('[InstagramDirect] URL completa:', url);
      console.log('[InstagramDirect] Método HTTP: POST');
      console.log('[InstagramDirect] Content-Type: application/json');

      console.log('[InstagramDirect] STEP 3: Enviando requisição para Instagram API...');
      const requestStartTime = new Date().getTime();

      const response = await axios.post(url, iceBreakersConfig, {
        params: {
          access_token: accessToken
        },
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const requestEndTime = new Date().getTime();
      const requestDuration = requestEndTime - requestStartTime;

      console.log('[InstagramDirect] STEP 4: Resposta recebida com sucesso!');
      console.log('[InstagramDirect] Tempo da requisição:', requestDuration + 'ms');
      console.log('[InstagramDirect] Status da resposta:', response.status);
      console.log('[InstagramDirect] Headers da resposta:', response.headers);
      console.log('[InstagramDirect] Dados da resposta completos:', JSON.stringify(response.data, null, 2));

      const totalTime = new Date().getTime() - startTime;
      console.log('[InstagramDirect] SUCESSO: Ice-breakers configurados em', totalTime + 'ms');
      console.log('[InstagramDirect] =====================================');

      return Resposta.sucesso(response.data);

    } catch (error: any) {
      const totalTime = new Date().getTime() - startTime;

      console.error('[InstagramDirect] =====================================');
      console.error('[InstagramDirect] ERRO AO CONFIGURAR ICE-BREAKERS');
      console.error('[InstagramDirect] Tempo até erro:', totalTime + 'ms');
      console.error('[InstagramDirect] Timestamp do erro:', new Date().toISOString());

      // Log detalhado do erro
      console.error('[InstagramDirect] Erro completo:', error);
      console.error('[InstagramDirect] Error name:', error.name);
      console.error('[InstagramDirect] Error message:', error.message);

      if (error.response) {
        console.error('[InstagramDirect] Resposta de erro HTTP:');
        console.error('[InstagramDirect] Status:', error.response.status);
        console.error('[InstagramDirect] Status text:', error.response.statusText);
        console.error('[InstagramDirect] Headers:', error.response.headers);
        console.error('[InstagramDirect] Data completa:', JSON.stringify(error.response.data, null, 2));

        // Log do request que falhou
        if (error.config) {
          console.error('[InstagramDirect] Request que falhou:');
          console.error('[InstagramDirect] URL:', error.config.url);
          console.error('[InstagramDirect] Method:', error.config.method?.toUpperCase());
          console.error('[InstagramDirect] Headers enviados:', error.config.headers);
          console.error('[InstagramDirect] Data enviada:', error.config.data);
        }

        // Log de trace IDs se disponível
        if (error.response.data?.error?.fbtrace_id) {
          console.error('[InstagramDirect] Facebook Trace ID:', error.response.data.error.fbtrace_id);
        }
      } else if (error.request) {
        console.error('[InstagramDirect] Erro de rede/timeout:');
        console.error('[InstagramDirect] Request:', error.request);
      }

      let mensagemErro = 'Erro ao configurar ice-breakers';
      if (error.response?.data?.error?.message) {
        mensagemErro = error.response.data.error.message;
      }

      console.error('[InstagramDirect] Mensagem de erro final:', mensagemErro);
      console.error('[InstagramDirect] =====================================');

      return Resposta.erro(mensagemErro);
    }
  }
}
