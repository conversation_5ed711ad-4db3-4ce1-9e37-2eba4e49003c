import {<PERSON>re<PERSON>s} from "../lib/Tarefas";
import {App} from '../App';
import {EnviadorDeCarrinhoAbandonadoService} from "../service/EnviadorDeCarrinhoAbandonadoService";
import {SessaoLinkSaudacao} from "../domain/SessaoLinkSaudacao";
import {Contato} from "../domain/Contato";
import {Empresa} from "../domain/Empresa";
import {TarefasDev} from "../lib/TarefasDev";
import * as fs from 'fs';
import * as path from 'path';
process.send = process.send || function () { return false };

// 🔒 LOCK DE ARQUIVO - Impede múltiplas instâncias
const LOCK_FILE = path.join(__dirname, '../../tarefas.lock');

function criarLock(): boolean {
  try {
    // Verifica se já existe um lock
    if (fs.existsSync(LOCK_FILE)) {
      const lockContent = fs.readFileSync(LOCK_FILE, 'utf8');
      const lockData = JSON.parse(lockContent);

      // Verifica se o processo ainda está rodando
      try {
        process.kill(lockData.pid, 0); // Não mata, apenas verifica se existe
        console.log(`❌ Outra instância já está rodando (PID: ${lockData.pid})`);
        console.log('🚫 Encerrando esta instância para evitar duplicação...');
        return false;
      } catch (e) {
        // Processo não existe mais, remove lock órfão
        console.log('🧹 Removendo lock órfão...');
        fs.unlinkSync(LOCK_FILE);
      }
    }

    // Cria novo lock
    const lockData = {
      pid: process.pid,
      startTime: new Date().toISOString(),
      hostname: require('os').hostname()
    };

    fs.writeFileSync(LOCK_FILE, JSON.stringify(lockData, null, 2));
    console.log(`✅ Lock criado com sucesso (PID: ${process.pid})`);

    // Remove lock quando o processo terminar
    process.on('exit', () => {
      try {
        if (fs.existsSync(LOCK_FILE)) {
          fs.unlinkSync(LOCK_FILE);
          console.log('🧹 Lock removido na saída do processo');
        }
      } catch (e) {
        console.error('Erro ao remover lock:', e);
      }
    });

    // Remove lock em caso de erro
    process.on('SIGINT', () => {
      try {
        if (fs.existsSync(LOCK_FILE)) {
          fs.unlinkSync(LOCK_FILE);
          console.log('🧹 Lock removido (SIGINT)');
        }
      } catch (e) {
        console.error('Erro ao remover lock:', e);
      }
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      try {
        if (fs.existsSync(LOCK_FILE)) {
          fs.unlinkSync(LOCK_FILE);
          console.log('🧹 Lock removido (SIGTERM)');
        }
      } catch (e) {
        console.error('Erro ao remover lock:', e);
      }
      process.exit(0);
    });

    return true;
  } catch (error) {
    console.error('❌ Erro ao criar lock:', error);
    return false;
  }
}

// Verifica lock antes de iniciar
if (!criarLock()) {
  console.log('🛑 Encerrando processo devido a instância duplicada');
  process.exit(1);
}

console.log('🚀 Iniciando sistema de tarefas...');

const app = new App()
app.configureAmbiente();
app.configureBancoDeDados();

const enviador = EnviadorDeCarrinhoAbandonadoService.Instancia();
const sessao = new SessaoLinkSaudacao();
sessao.empresa = new Empresa();
sessao.empresa.id = 1;
sessao.empresa.nome = 'Fibonacci';

sessao.contato = new Contato(21046, 'Márcio', '62982301144', null, null, null, null, '55');

enviador.execute(sessao, 3);

Tarefas.inicie();

//new TarefasDev().inicie()

process.send('ready');
