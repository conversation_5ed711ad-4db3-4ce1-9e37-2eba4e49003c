module.exports = {
  apps: [
    {
      name: 'tarefas',
      script: 'distServer/bin/tarefas.js',
      instances: 1, // 🔒 APENAS UMA INSTÂNCIA
      exec_mode: 'fork', // Não cluster
      watch: false,
      max_memory_restart: '4G',
      node_args: '--max-old-space-size=4192',
      env: {
        NODE_ENV: 'production'
      },
      // Configurações para evitar duplicação
      wait_ready: true,
      listen_timeout: 60000,
      kill_timeout: 5000,
      max_restarts: 3,
      min_uptime: '10s',
      // Log configurações
      log_file: 'logs/tarefas.log',
      out_file: 'logs/tarefas-out.log',
      error_file: 'logs/tarefas-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      // Configurações de restart
      autorestart: true,
      restart_delay: 4000,
      // Configurações avançadas
      ignore_watch: ['node_modules', 'logs'],
      instance_var: 'INSTANCE_ID'
    }
  ]
};
