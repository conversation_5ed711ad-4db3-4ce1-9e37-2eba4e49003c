. ~/.nvm/nvm.sh
export NODE_ENV=production
nvm use 12

export NODE_OPTIONS=--max-old-space-size=4192

tsc --p server

grunt copy
echo $PWD

# Parar e remover instâncias existentes para evitar duplicação
pm2 stop tarefas 2>/dev/null || true
pm2 delete tarefas 2>/dev/null || true

# Iniciar apenas UMA instância com nome específico
pm2 start distServer/bin/tarefas.js \
  --name "tarefas" \
  -i 1 \
  --wait-ready \
  --listen-timeout 60000 \
  --node-args="--max-old-space-size=4192" \
  --max-restarts 3 \
  --min-uptime 10s

echo "Finalizando..."
